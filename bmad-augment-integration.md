# BMAD-METHOD与Augment Code集成任务

## 任务：augment-code-integration

### 目标
将BMAD-METHOD的敏捷开发方法论集成到Augment Code工作流中。

### 前置条件
- 已安装BMAD-METHOD到项目根目录
- 配置了技术偏好文件
- 准备了项目文档结构

### 执行步骤

#### 1. 项目分析阶段
```bash
# 使用代码库扁平化工具分析现有代码
npx bmad-method flatten --output current-codebase.xml

# 生成项目文档索引
npx bmad-method list:agents
```

#### 2. 规划阶段集成
- 使用Analyst代理进行需求分析
- 通过PM代理创建或更新PRD
- 利用Architect代理优化架构设计
- 应用PO代理验证需求对齐

#### 3. 开发阶段集成
- 使用SM代理起草开发故事
- 在Augment Code中实现故事任务
- 应用QA代理进行代码审查
- 遵循BMAD的质量检查清单

#### 4. 持续改进
- 定期更新技术偏好
- 优化代理配置
- 收集反馈并调整工作流

### 输出产物
- 结构化的项目文档
- 详细的开发故事
- 质量保证检查清单
- 技术债务跟踪

### 成功指标
- 开发效率提升
- 代码质量改善
- 文档完整性增强
- 团队协作优化

## 最佳实践

### 1. 代理使用策略
- 规划阶段：使用Web UI代理（成本效益高）
- 开发阶段：在IDE中应用方法论
- 审查阶段：结合QA代理和人工审查

### 2. 文档管理
- 保持PRD和架构文档同步
- 定期分片大型文档
- 使用版本控制跟踪变更

### 3. 质量控制
- 每个故事完成后运行回归测试
- 提交前进行代码质量检查
- 定期进行架构审查

### 4. 团队协作
- 明确代理角色分工
- 建立清晰的交接流程
- 保持沟通记录

## 故障排除

### 常见问题
1. **代理依赖缺失**
   - 检查.bmad-core目录完整性
   - 重新运行安装命令

2. **文档格式错误**
   - 验证YAML格式正确性
   - 检查模板引用路径

3. **性能问题**
   - 定期清理上下文
   - 优化代理配置
   - 使用文档分片

### 调试命令
```bash
# 验证配置
npx bmad-method validate

# 列出可用代理
npx bmad-method list:agents

# 构建Web包
npx bmad-method build
```

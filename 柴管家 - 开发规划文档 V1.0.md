# **柴管家 \- 开发规划文档 V1.0**

文档状态: 已确认  
创建日期: 2025-08-01  
核心贡献者: BDD开发规划专家, 项目负责人

## **1\. 总体开发哲学与框架**

本项目将采用\*\*行为驱动开发（BDD）\*\*作为核心开发策略，以确保AI的开发工作与业务需求精确对齐，并实现最大化的开发效率和质量保证。

AI开发者的工作将严格遵循以下“三步走”的固定流程：

graph TD  
    A\[1. 编写行为剧本\] \--\> B\[2. 编写自动化测试\];  
    B \--\> C\[3. 编写产品代码\];  
    C \--\> D{测试通过?};  
    D \-- 是 \--\> E\[功能完成\];  
    D \-- 否 \--\> C;

* **1\. 编写行为剧本 (Gherkin Feature Files):** 由项目负责人与开发教练共同编写，用自然语言精确定义功能的预期行为，作为给AI开发者的核心指令。  
* **2\. 编写自动化测试:** AI开发者基于“剧本”生成一个当前会失败的自动化测试。  
* **3\. 编写产品代码:** AI开发者编写刚好能让该测试通过的最精简代码。

此流程循环往复，直至所有功能开发完成。

## **2\. 范围定义与需求分解**

### **2.1 MVP实施策略**

我们将采纳**方案B：端到端故事切片**作为MVP的实施策略。首要目标是尽快完成一个最简化但完整的用户体验闭环，快速验证整个流程并建立信心。

### **2.2 MVP史诗（Epics）分解**

MVP的开发范围将围绕以下四个核心史诗展开。详细的用户故事（User Stories）将在项目管理工具的待办列表（Backlog）中进行维护。

* **史诗 1: 核心渠道管理 (Epic: Core Channel Management)**  
  * **定义:** 此史诗的目标是构建系统的基础连接能力。它涵盖了允许用户通过授权流程，将不同平台（如闲鱼、微信等）的多个账号安全地接入到“柴管家”系统中，并对这些接入的账号进行统一管理（如设置别名、监控在线状态）的功能。  
* **史诗 2: 统一消息工作台 (Epic: Unified Messaging Workspace)**  
  * **定义:** 此史诗聚焦于为运营者提供一个核心操作界面。其目标是将所有已接入渠道账号的消息，实时、无遗漏地汇聚到一个统一的对话列表中，并允许运营者直接在此界面上通过原始渠道回复任何消息，从而实现跨平台沟通的效率提升。  
* **史诗** 3: AI副驾与知识库 (Epic: AI Copilot **& Knowledge Base)**  
  * **定义:** 此史诗旨在为人工回复“提效赋能”。它包括两个核心部分：一是提供一个基础的知识库（FAQ）管理功能，供运营者录入标准问答；二是在运营者进行人工回复时，AI能实时分析对话上下文，并从知识库中匹配信息，生成回复建议供运营者一键选用。  
* **史诗 4: AI托管与人工接管 (Epic: AI Hosting & Manual Takeover)**  
  * **定义:** 此史诗的目标是实现运营的“自动化”。它要求系统能够对单个会话开启“AI托管模式”。在该模式下，AI能基于置信度判断自主回复；当遇到无法处理的复杂问题（低置信度）时，系统必须能自动暂停AI并清晰地提醒运营者介入，同时确保运营者可以随时无缝地接管任何对话，保证服务质量与安全。

## **3\. 实施路线图与迭代规划**

每个开发冲刺（Sprint）的周期定为**2周**。

gantt  
    title 柴管家 \- 开发路线图  
    dateFormat  YYYY-MM-DD  
    axisFormat %m-%d  
      
    section MVP核心闭环 (M1)  
    环境搭建与CI/CD (S0) :done, 2025-08-04, 1w  
    闲鱼连接器集成与手动收发 (S1) :active, 2025-08-11, 2w  
      
    section AI能力增强 (M2)  
    知识库与AI副驾 (S2) :2025-08-25, 2w  
    AI托管与人工接管 (S3) :2025-09-08, 2w

## **4\. 质量保证与验收标准**

### **4.1 完成的定义 (Definition of Done \- DoD)**

所有用户故事必须满足以下所有条件，才能被视为“已完成”：

1. 代码已通过CI/CD流水线的所有自动化检查。  
2. 与该功能相关的所有“验收标准（AC）”都已通过自动化测试。  
3. 单元测试已编写并通过，核心逻辑的代码覆盖率不低于85%。  
4. 代码已成功合并到主干开发分支。  
5. 相关的API文档已通过自动化工具生成并更新。

### **4.2 验收标准 (Acceptance Criteria \- AC) 范例**

我们将结合**可视化流程图**和**Gherkin剧本**来定义AC。以“AI低置信度时自动转交人工”功能为例：

**可视化流程:**

graph TD  
    A\[AI托管模式下收到新消息\] \--\> B{AI分析并计算置信度};  
    B \--\> C{置信度 \>= 0.8?};  
    C \-- 是 \--\> D\[AI自动发送回复\];  
    C \-- 否 \--\> E\[暂停AI并高亮通知\];  
    E \--\> F\[标记为"待人工接管"\];

**Gherkin剧本:**

**场景:** 低置信度回复触发人工接管

假如 一个会话处于“AI托管模式”  
并且 系统设定的置信度阈值为0.8  
当 AI针对新消息生成了一条置信度为0.6的回复  
那么 系统绝不能自动发送该回复  
并且 该会话的状态必须自动切换为“待人工接管”  
并且 系统必须在界面上高亮该会话以通知运营者

## **5\. 过程监控与可验证性**

### **5.1 后端验证标准**

当AI开发者完成一个后端用户故事时，其交付物必须包含：

1. **一份可交互的API文档** (如Swagger UI)。  
2. **一个专用的后端测试面板** (一个简易的、用于手动调用API的HTML页面)。  
3. **一份冒烟测试报告** (来自CI/CD流水线)。  
4. **结构化的日志输出**。

### **5.2 前端验证标准**

当AI开发者完成一个前端用户故事时，其交付物必须包含：

1. **一个可操作的预览环境** (部署在Staging环境)。  
2. **一份端到端(E2E)测试报告**。  
3. **一份基于AC的用户验收清单** (Checklist)。

## **6\. 风险管理**

| 风险描述 | 优先级 | 应对策略 |
| :---- | :---- | :---- |
| **第三方接口稳定性风险** | 高 | **架构隔离**：连接器模块独立，故障不影响核心。\<br\>**自动化哨兵**：为连接器编写全面的集成测试并加入CI/CD，接口变更可即时发现。 |
| **AI开发效果不及预期风险** | 中 | **BDD作为精确指令**：Gherkin剧本约束AI行为。\<br\>**DoD作为质量门禁**：自动化检查代码质量。\<br\>**关键节点人工审查**：对核心逻辑和安全代码进行Code Review。 |
| **需求蔓延 (Scope Creep) 风险** | 中 | **路线图作为防火墙**：新需求进入待办列表评估，而非直接插入当前冲刺。\<br\>**价值导向决策**：通过“替换”而非“增加”来控制总工作量。 |

